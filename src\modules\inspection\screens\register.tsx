"use client";

import { pathService } from "@/config/path-manager/service";
import type { ComponentType } from "react";
import { RegisterInspectionTabs } from "../components/register/register-inspection-tabs";

export const RegisterInspectionScreen = () => {
	const item = pathService.getItemById("inspection-register");
	const Icon = item?.icon as ComponentType<unknown> | undefined;

	return (
		<div className="container mx-auto flex h-full min-h-full flex-1 flex-col space-y-6 py-1">
			<header className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
				<div className="flex items-center gap-4">
					<div className="bg-primary flex h-12 w-12 items-center justify-center rounded-lg text-white">{Icon ? <Icon /> : null}</div>
					<div>
						<h1 className="text-2xl font-semibold text-gray-900">{item?.name}</h1>
						<p className="mt-1 text-sm text-gray-500">{item?.description}</p>
					</div>
				</div>
				<div className="flex gap-3"></div>
			</header>

			<RegisterInspectionTabs />
		</div>
	);
};
